@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-white text-gray-900 font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }

  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 px-4 py-2;
  }

  .btn-secondary {
    @apply btn bg-secondary-200 text-secondary-800 hover:bg-secondary-300 px-4 py-2;
  }

  .btn-outline {
    @apply btn border border-primary-300 text-primary-600 hover:bg-primary-50 px-4 py-2;
  }

  .btn-ghost {
    @apply btn text-secondary-600 hover:bg-secondary-100 px-4 py-2;
  }

  .card {
    @apply rounded-lg border bg-white shadow-sm p-6;
  }

  .input {
    @apply flex h-10 w-full rounded-md border border-secondary-300 bg-white px-3 py-2 text-sm placeholder:text-secondary-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
}
